"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,SwatchIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { theme: nextTheme, setTheme: setNextTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const { currentTheme, themeName, switchTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    // Fix hydration issue with next-themes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // Handle scroll effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Navigation items\n    const navItems = [\n        {\n            key: \"features\",\n            href: \"#features\"\n        },\n        {\n            key: \"howItWorks\",\n            href: \"#how-it-works\"\n        },\n        {\n            key: \"pricing\",\n            href: \"#pricing\"\n        },\n        {\n            key: \"about\",\n            href: \"#about\"\n        },\n        {\n            key: \"contact\",\n            href: \"#contact\"\n        }\n    ];\n    // Language toggle\n    const toggleLanguage = ()=>{\n        const newLocale = locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    // Theme toggle (Next.js theme)\n    const toggleNextTheme = ()=>{\n        setNextTheme(nextTheme === \"dark\" ? \"light\" : \"dark\");\n    };\n    // Custom theme toggle (Gold/Purple)\n    const toggleCustomTheme = ()=>{\n        const newTheme = themeName === \"gold\" ? \"purple\" : \"gold\";\n        switchTheme(newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500\",\n        style: {\n            background: isScrolled ? \"\".concat(currentTheme.colors.glass.background, \", \").concat(currentTheme.backgrounds.secondary) : \"transparent\",\n            backdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            WebkitBackdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            borderBottom: isScrolled ? \"1px solid \".concat(currentTheme.colors.glass.border) : \"none\",\n            boxShadow: isScrolled ? currentTheme.colors.glass.shadow : \"none\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto container-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 rtl:space-x-reverse group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"relative w-8 h-8 lg:w-10 lg:h-10 rounded-lg flex items-center justify-center overflow-hidden\",\n                                    style: {\n                                        background: currentTheme.gradients.primary,\n                                        boxShadow: currentTheme.shadows.md\n                                    },\n                                    whileHover: {\n                                        scale: 1.05,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.BriefcaseIcon, {\n                                            className: \"w-5 h-5 lg:w-6 lg:h-6 text-white relative z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                    className: \"text-xl lg:text-2xl font-bold \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                    style: {\n                                        backgroundImage: currentTheme.gradients.text,\n                                        backgroundClip: \"text\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        textShadow: \"0 2px 4px rgba(0, 0, 0, 0.3)\"\n                                    },\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: isRTL ? \"فريلا سوريا\" : \"Freela Syria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8 rtl:space-x-reverse\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                    href: item.href,\n                                    className: \"relative font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                    style: {\n                                        color: currentTheme.colors.text.secondary\n                                    },\n                                    onClick: (e)=>{\n                                        var _document_querySelector;\n                                        e.preventDefault();\n                                        (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                            behavior: \"smooth\"\n                                        });\n                                    },\n                                    whileHover: {\n                                        scale: 1.05,\n                                        color: currentTheme.colors.text.accent\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: [\n                                        t(\"navigation.\".concat(item.key)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute bottom-0 left-0 h-0.5 rounded-full\",\n                                            style: {\n                                                background: currentTheme.gradients.primary\n                                            },\n                                            initial: {\n                                                width: 0\n                                            },\n                                            whileHover: {\n                                                width: \"100%\"\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.key, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleCustomTheme,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle custom theme\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SwatchIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleNextTheme,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle dark/light theme\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        mounted ? nextTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleLanguage,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle language\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.LanguageIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium relative z-10 \".concat(isRTL ? \"font-cairo\" : \"font-sans\"),\n                                            style: {\n                                                color: currentTheme.colors.text.primary\n                                            },\n                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            passHref: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                className: \"font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                style: {\n                                                    color: currentTheme.colors.text.accent\n                                                },\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    color: currentTheme.colors.text.primary\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: t(\"navigation.login\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            passHref: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                className: \"relative px-6 py-2.5 rounded-xl font-semibold overflow-hidden group \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                style: {\n                                                    background: currentTheme.gradients.button,\n                                                    color: \"white\",\n                                                    boxShadow: currentTheme.shadows.md,\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.3)\"\n                                                },\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: t(\"navigation.joinAsExpert\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                        style: {\n                                                            background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                            animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            type: \"button\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"lg:hidden relative p-2 rounded-lg overflow-hidden group\",\n                            style: {\n                                background: currentTheme.colors.glass.background,\n                                backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                            },\n                            \"aria-label\": \"Toggle menu\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    animate: {\n                                        rotate: isMenuOpen ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.XMarkIcon, {\n                                        className: \"w-6 h-6 relative z-10\",\n                                        style: {\n                                            color: currentTheme.colors.text.accent\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.Bars3Icon, {\n                                        className: \"w-6 h-6 relative z-10\",\n                                        style: {\n                                            color: currentTheme.colors.text.accent\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    style: {\n                                        background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                        animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\",\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0,\n                            y: -20\n                        },\n                        transition: {\n                            duration: 0.4,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"lg:hidden overflow-hidden\",\n                        style: {\n                            background: \"\".concat(currentTheme.colors.glass.background, \", \").concat(currentTheme.backgrounds.secondary),\n                            backdropFilter: currentTheme.colors.glass.backdropBlur,\n                            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                            borderTop: \"1px solid \".concat(currentTheme.colors.glass.border),\n                            boxShadow: currentTheme.colors.glass.shadow\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6 space-y-6\",\n                            children: [\n                                navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                        href: item.href,\n                                        className: \"block font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal text-right\" : \"font-sans text-left\"),\n                                        style: {\n                                            color: currentTheme.colors.text.secondary\n                                        },\n                                        onClick: (e)=>{\n                                            var _document_querySelector;\n                                            e.preventDefault();\n                                            setIsMenuOpen(false);\n                                            (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                                behavior: \"smooth\"\n                                            });\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            x: isRTL ? 20 : -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1,\n                                            duration: 0.3\n                                        },\n                                        whileHover: {\n                                            scale: 1.02,\n                                            color: currentTheme.colors.text.accent,\n                                            x: isRTL ? -5 : 5\n                                        },\n                                        children: t(\"navigation.\".concat(item.key))\n                                    }, item.key, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"flex items-center justify-between pt-6 mt-6\",\n                                    style: {\n                                        borderTop: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.3,\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: toggleCustomTheme,\n                                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                                    style: {\n                                                        background: currentTheme.colors.glass.background,\n                                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SwatchIcon, {\n                                                        className: \"w-5 h-5 relative z-10\",\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: toggleNextTheme,\n                                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                                    style: {\n                                                        background: currentTheme.colors.glass.background,\n                                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: mounted ? nextTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                                        className: \"w-5 h-5 relative z-10\",\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                                        className: \"w-5 h-5 relative z-10\",\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: toggleLanguage,\n                                                    className: \"relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse\",\n                                                    style: {\n                                                        background: currentTheme.colors.glass.background,\n                                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.LanguageIcon, {\n                                                            className: \"w-5 h-5 relative z-10\",\n                                                            style: {\n                                                                color: currentTheme.colors.text.accent\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium relative z-10 \".concat(isRTL ? \"font-cairo\" : \"font-sans\"),\n                                                            style: {\n                                                                color: currentTheme.colors.text.primary\n                                                            },\n                                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                        className: \"font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        },\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        whileHover: {\n                                                            scale: 1.05,\n                                                            color: currentTheme.colors.text.primary\n                                                        },\n                                                        transition: {\n                                                            duration: 0.2\n                                                        },\n                                                        children: t(\"navigation.login\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/signup\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                        className: \"relative px-4 py-2 text-sm rounded-xl font-semibold overflow-hidden group \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                        style: {\n                                                            background: currentTheme.gradients.button,\n                                                            color: \"white\",\n                                                            boxShadow: currentTheme.shadows.md,\n                                                            textShadow: \"0 1px 2px rgba(0, 0, 0, 0.3)\"\n                                                        },\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        whileHover: {\n                                                            scale: 1.05,\n                                                            y: -2\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        transition: {\n                                                            duration: 0.2\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"relative z-10\",\n                                                                children: t(\"navigation.joinAsExpert\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                style: {\n                                                                    background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                                    animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"rrXTuHAe54Vm539jblkk+3aklEA=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme,\n        _themes__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Header.tsx\n"));

/***/ })

});