"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_HeartIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,HeartIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BriefcaseIcon,HeartIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Footer() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        x: 0,\n        y: 0\n    });\n    const { currentTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Get footer links from translation\n    const footerSections = [\n        {\n            title: t(\"footer.links.company.title\"),\n            links: Array.from({\n                length: 4\n            }, (_, i)=>({\n                    label: t(\"footer.links.company.items.\".concat(i, \".label\")),\n                    href: t(\"footer.links.company.items.\".concat(i, \".href\"))\n                }))\n        },\n        {\n            title: t(\"footer.links.services.title\"),\n            links: Array.from({\n                length: 4\n            }, (_, i)=>({\n                    label: t(\"footer.links.services.items.\".concat(i, \".label\")),\n                    href: t(\"footer.links.services.items.\".concat(i, \".href\"))\n                }))\n        },\n        {\n            title: t(\"footer.links.legal.title\"),\n            links: Array.from({\n                length: 4\n            }, (_, i)=>({\n                    label: t(\"footer.links.legal.items.\".concat(i, \".label\")),\n                    href: t(\"footer.links.legal.items.\".concat(i, \".href\"))\n                }))\n        }\n    ];\n    const socialLinks = [\n        {\n            name: \"Facebook\",\n            href: t(\"footer.social.facebook\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Twitter\",\n            href: t(\"footer.social.twitter\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"LinkedIn\",\n            href: t(\"footer.social.linkedin\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Instagram\",\n            href: t(\"footer.social.instagram\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, \").concat(currentTheme.colors.accent[500], \"25 0%, transparent 60%),\\n          radial-gradient(circle at \").concat(100 - mousePosition.x, \"% \").concat(100 - mousePosition.y, \"%, \").concat(currentTheme.colors.secondary[500], \"20 0%, transparent 60%),\\n          \").concat(currentTheme.backgrounds.tertiary || currentTheme.backgrounds.secondary, \"\\n        \"),\n            color: currentTheme.colors.text.primary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                -20,\n                                20,\n                                -20\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.05,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 left-10 w-28 h-28 rounded-full opacity-15\",\n                        style: {\n                            background: currentTheme.colors.glass.background,\n                            backdropFilter: currentTheme.colors.glass.backdropBlur,\n                            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                            border: \"1px solid \".concat(currentTheme.colors.glass.border),\n                            boxShadow: currentTheme.colors.glass.shadow\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                30,\n                                -30,\n                                30\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.05,\n                                1,\n                                1.05\n                            ]\n                        },\n                        transition: {\n                            duration: 30,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-40 right-20 w-20 h-20 rounded-full opacity-12\",\n                        style: {\n                            background: \"\".concat(currentTheme.colors.secondary[500], \"20\"),\n                            backdropFilter: \"blur(12px)\",\n                            WebkitBackdropFilter: \"blur(12px)\",\n                            border: \"1px solid \".concat(currentTheme.colors.secondary[500], \"30\"),\n                            boxShadow: \"0 8px 32px \".concat(currentTheme.colors.secondary[500], \"20\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            animate: {\n                                y: [\n                                    -15,\n                                    15,\n                                    -15\n                                ],\n                                x: [\n                                    -8,\n                                    8,\n                                    -8\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.25,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.15,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 12 + i * 3,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 1\n                            },\n                            className: \"absolute w-1.5 h-1.5 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(15 + i * 15, \"%\"),\n                                top: \"\".concat(25 + i * 10, \"%\"),\n                                filter: \"blur(0.5px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/\",\n                                                className: \"flex items-center space-x-3 rtl:space-x-reverse mb-8 group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        className: \"w-12 h-12 rounded-xl flex items-center justify-center relative overflow-hidden group\",\n                                                        style: {\n                                                            background: currentTheme.colors.glass.background,\n                                                            backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                            border: \"1px solid \".concat(currentTheme.colors.glass.border),\n                                                            boxShadow: currentTheme.shadows.lg\n                                                        },\n                                                        whileHover: {\n                                                            scale: 1.05,\n                                                            rotate: 5\n                                                        },\n                                                        transition: {\n                                                            duration: 0.2\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BriefcaseIcon_HeartIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.BriefcaseIcon, {\n                                                                className: \"w-7 h-7 group-hover:scale-110 transition-transform duration-300 relative z-10\",\n                                                                style: {\n                                                                    color: currentTheme.colors.text.primary\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BriefcaseIcon_HeartIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.SparklesIcon, {\n                                                                className: \"w-4 h-4 absolute -top-1 -right-1 animate-pulse relative z-10\",\n                                                                style: {\n                                                                    color: currentTheme.colors.text.accent\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                style: {\n                                                                    background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                                    animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-arabic-premium \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                        style: {\n                                                            backgroundImage: currentTheme.gradients.text,\n                                                            backgroundClip: \"text\",\n                                                            WebkitBackgroundClip: \"text\",\n                                                            WebkitTextFillColor: \"transparent\",\n                                                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.3)\"\n                                                        },\n                                                        children: isRTL ? \"فريلا سوريا\" : \"Freela Syria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-8 max-w-md leading-relaxed text-lg text-arabic \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                style: {\n                                                    color: currentTheme.colors.text.secondary\n                                                },\n                                                children: t(\"footer.description\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                                                children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.a, {\n                                                        href: social.href,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group relative overflow-hidden\",\n                                                        style: {\n                                                            background: currentTheme.colors.glass.background,\n                                                            backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                            border: \"1px solid \".concat(currentTheme.colors.glass.border),\n                                                            boxShadow: currentTheme.shadows.md\n                                                        },\n                                                        whileHover: {\n                                                            scale: 1.1,\n                                                            y: -2,\n                                                            background: index === 0 ? \"\".concat(currentTheme.colors.accent[500], \"40\") : index === 1 ? \"\".concat(currentTheme.colors.secondary[500], \"40\") : \"\".concat(currentTheme.colors.primary[500], \"40\")\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        \"aria-label\": social.name,\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.6,\n                                                            delay: 0.1 + index * 0.1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"group-hover:scale-110 transition-transform duration-300 relative z-10\",\n                                                                style: {\n                                                                    color: currentTheme.colors.text.primary\n                                                                },\n                                                                children: social.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                style: {\n                                                                    background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                                    animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, social.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                footerSections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.2 + index * 0.1\n                                        },\n                                        className: \"p-6 rounded-2xl relative overflow-hidden group\",\n                                        style: {\n                                            background: currentTheme.colors.glass.background,\n                                            backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                            border: \"1px solid \".concat(currentTheme.colors.glass.border),\n                                            boxShadow: currentTheme.shadows.md\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-2 right-2 opacity-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-8 rounded-full\",\n                                                    style: {\n                                                        background: currentTheme.colors.primary[500]\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-6 text-lg text-arabic-premium \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                style: {\n                                                    color: currentTheme.colors.text.primary\n                                                },\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-4\",\n                                                children: section.links.map((link, linkIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.li, {\n                                                        whileHover: {\n                                                            x: 5\n                                                        },\n                                                        transition: {\n                                                            duration: 0.2\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"transition-all duration-300 text-arabic hover:underline underline-offset-4 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                            style: {\n                                                                color: currentTheme.colors.text.secondary,\n                                                                textDecorationColor: \"\".concat(currentTheme.colors.text.muted, \"50\")\n                                                            },\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, linkIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-8 mt-8\",\n                        style: {\n                            borderTop: \"1px solid \".concat(currentTheme.colors.glass.border),\n                            background: currentTheme.colors.glass.background,\n                            backdropFilter: \"blur(10px)\",\n                            WebkitBackdropFilter: \"blur(10px)\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row items-center justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.5\n                                    },\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-arabic \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                            style: {\n                                                color: currentTheme.colors.text.muted\n                                            },\n                                            children: t(\"footer.copyright\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BriefcaseIcon_HeartIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.HeartIcon, {\n                                            className: \"w-4 h-4 animate-pulse\",\n                                            style: {\n                                                color: currentTheme.colors.accent[500]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: \"flex items-center space-x-8 rtl:space-x-reverse text-sm\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.6\n                                    },\n                                    children: [\n                                        {\n                                            href: \"/terms\",\n                                            label: isRTL ? \"شروط الاستخدام\" : \"Terms of Service\"\n                                        },\n                                        {\n                                            href: \"/privacy\",\n                                            label: isRTL ? \"سياسة الخصوصية\" : \"Privacy Policy\"\n                                        },\n                                        {\n                                            href: \"/cookies\",\n                                            label: isRTL ? \"سياسة الكوكيز\" : \"Cookie Policy\"\n                                        }\n                                    ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            whileHover: {\n                                                y: -2\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"transition-all duration-300 text-arabic hover:underline underline-offset-4 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                style: {\n                                                    color: currentTheme.colors.text.muted,\n                                                    textDecorationColor: \"\".concat(currentTheme.colors.text.secondary, \"50\")\n                                                },\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"b7yceM1gqcCq1Oto01AHbr3P+Io=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _themes__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Footer.tsx\n"));

/***/ })

});