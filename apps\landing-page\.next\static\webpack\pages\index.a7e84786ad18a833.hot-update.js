"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,SwatchIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { theme: nextTheme, setTheme: setNextTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const { currentTheme, themeName, switchTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    // Fix hydration issue with next-themes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // Handle scroll effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Navigation items\n    const navItems = [\n        {\n            key: \"features\",\n            href: \"#features\"\n        },\n        {\n            key: \"howItWorks\",\n            href: \"#how-it-works\"\n        },\n        {\n            key: \"pricing\",\n            href: \"#pricing\"\n        },\n        {\n            key: \"about\",\n            href: \"#about\"\n        },\n        {\n            key: \"contact\",\n            href: \"#contact\"\n        }\n    ];\n    // Language toggle\n    const toggleLanguage = ()=>{\n        const newLocale = locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    // Theme toggle (Next.js theme)\n    const toggleNextTheme = ()=>{\n        setNextTheme(nextTheme === \"dark\" ? \"light\" : \"dark\");\n    };\n    // Custom theme toggle (Gold/Purple)\n    const toggleCustomTheme = ()=>{\n        const newTheme = themeName === \"gold\" ? \"purple\" : \"gold\";\n        switchTheme(newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500\",\n        style: {\n            background: isScrolled ? \"\".concat(currentTheme.colors.glass.background, \", \").concat(currentTheme.backgrounds.secondary) : \"transparent\",\n            backdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            WebkitBackdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            borderBottom: isScrolled ? \"1px solid \".concat(currentTheme.colors.glass.border) : \"none\",\n            boxShadow: isScrolled ? currentTheme.colors.glass.shadow : \"none\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto container-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 rtl:space-x-reverse group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"relative w-8 h-8 lg:w-10 lg:h-10 rounded-lg flex items-center justify-center overflow-hidden\",\n                                    style: {\n                                        background: currentTheme.gradients.primary,\n                                        boxShadow: currentTheme.shadows.md\n                                    },\n                                    whileHover: {\n                                        scale: 1.05,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.BriefcaseIcon, {\n                                            className: \"w-5 h-5 lg:w-6 lg:h-6 text-white relative z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                    className: \"text-xl lg:text-2xl font-bold \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                    style: {\n                                        backgroundImage: currentTheme.gradients.text,\n                                        backgroundClip: \"text\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        textShadow: \"0 2px 4px rgba(0, 0, 0, 0.3)\"\n                                    },\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: isRTL ? \"فريلا سوريا\" : \"Freela Syria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8 rtl:space-x-reverse\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                    href: item.href,\n                                    className: \"relative font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                    style: {\n                                        color: currentTheme.colors.text.secondary\n                                    },\n                                    onClick: (e)=>{\n                                        var _document_querySelector;\n                                        e.preventDefault();\n                                        (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                            behavior: \"smooth\"\n                                        });\n                                    },\n                                    whileHover: {\n                                        scale: 1.05,\n                                        color: currentTheme.colors.text.accent\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: [\n                                        t(\"navigation.\".concat(item.key)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute bottom-0 left-0 h-0.5 rounded-full\",\n                                            style: {\n                                                background: currentTheme.gradients.primary\n                                            },\n                                            initial: {\n                                                width: 0\n                                            },\n                                            whileHover: {\n                                                width: \"100%\"\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.key, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleCustomTheme,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle custom theme\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SwatchIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleNextTheme,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle dark/light theme\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        mounted ? nextTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleLanguage,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle language\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.LanguageIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium relative z-10 \".concat(isRTL ? \"font-cairo\" : \"font-sans\"),\n                                            style: {\n                                                color: currentTheme.colors.text.primary\n                                            },\n                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                className: \"font-medium transition-all duration-300 cursor-pointer \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                style: {\n                                                    color: currentTheme.colors.text.accent\n                                                },\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    color: currentTheme.colors.text.primary\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: t(\"navigation.login\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                className: \"relative px-6 py-2.5 rounded-xl font-semibold overflow-hidden group cursor-pointer inline-block \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                style: {\n                                                    background: currentTheme.gradients.button,\n                                                    color: \"white\",\n                                                    boxShadow: currentTheme.shadows.md,\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.3)\"\n                                                },\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: t(\"navigation.joinAsExpert\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                        style: {\n                                                            background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                            animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            type: \"button\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"lg:hidden relative p-2 rounded-lg overflow-hidden group\",\n                            style: {\n                                background: currentTheme.colors.glass.background,\n                                backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                            },\n                            \"aria-label\": \"Toggle menu\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    animate: {\n                                        rotate: isMenuOpen ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.XMarkIcon, {\n                                        className: \"w-6 h-6 relative z-10\",\n                                        style: {\n                                            color: currentTheme.colors.text.accent\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.Bars3Icon, {\n                                        className: \"w-6 h-6 relative z-10\",\n                                        style: {\n                                            color: currentTheme.colors.text.accent\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    style: {\n                                        background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                        animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\",\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0,\n                            y: -20\n                        },\n                        transition: {\n                            duration: 0.4,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"lg:hidden overflow-hidden\",\n                        style: {\n                            background: \"\".concat(currentTheme.colors.glass.background, \", \").concat(currentTheme.backgrounds.secondary),\n                            backdropFilter: currentTheme.colors.glass.backdropBlur,\n                            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                            borderTop: \"1px solid \".concat(currentTheme.colors.glass.border),\n                            boxShadow: currentTheme.colors.glass.shadow\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6 space-y-6\",\n                            children: [\n                                navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                        href: item.href,\n                                        className: \"block font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal text-right\" : \"font-sans text-left\"),\n                                        style: {\n                                            color: currentTheme.colors.text.secondary\n                                        },\n                                        onClick: (e)=>{\n                                            var _document_querySelector;\n                                            e.preventDefault();\n                                            setIsMenuOpen(false);\n                                            (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                                behavior: \"smooth\"\n                                            });\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            x: isRTL ? 20 : -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1,\n                                            duration: 0.3\n                                        },\n                                        whileHover: {\n                                            scale: 1.02,\n                                            color: currentTheme.colors.text.accent,\n                                            x: isRTL ? -5 : 5\n                                        },\n                                        children: t(\"navigation.\".concat(item.key))\n                                    }, item.key, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"flex items-center justify-between pt-6 mt-6\",\n                                    style: {\n                                        borderTop: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.3,\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: toggleCustomTheme,\n                                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                                    style: {\n                                                        background: currentTheme.colors.glass.background,\n                                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SwatchIcon, {\n                                                        className: \"w-5 h-5 relative z-10\",\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: toggleNextTheme,\n                                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                                    style: {\n                                                        background: currentTheme.colors.glass.background,\n                                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: mounted ? nextTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                                        className: \"w-5 h-5 relative z-10\",\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                                        className: \"w-5 h-5 relative z-10\",\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: toggleLanguage,\n                                                    className: \"relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse\",\n                                                    style: {\n                                                        background: currentTheme.colors.glass.background,\n                                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.LanguageIcon, {\n                                                            className: \"w-5 h-5 relative z-10\",\n                                                            style: {\n                                                                color: currentTheme.colors.text.accent\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium relative z-10 \".concat(isRTL ? \"font-cairo\" : \"font-sans\"),\n                                                            style: {\n                                                                color: currentTheme.colors.text.primary\n                                                            },\n                                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                        className: \"font-medium transition-all duration-300 cursor-pointer \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                        style: {\n                                                            color: currentTheme.colors.text.accent\n                                                        },\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        whileHover: {\n                                                            scale: 1.05,\n                                                            color: currentTheme.colors.text.primary\n                                                        },\n                                                        transition: {\n                                                            duration: 0.2\n                                                        },\n                                                        children: t(\"navigation.login\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/signup\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                        className: \"relative px-4 py-2 text-sm rounded-xl font-semibold overflow-hidden group cursor-pointer inline-block \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                        style: {\n                                                            background: currentTheme.gradients.button,\n                                                            color: \"white\",\n                                                            boxShadow: currentTheme.shadows.md,\n                                                            textShadow: \"0 1px 2px rgba(0, 0, 0, 0.3)\"\n                                                        },\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        whileHover: {\n                                                            scale: 1.05,\n                                                            y: -2\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        transition: {\n                                                            duration: 0.2\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"relative z-10\",\n                                                                children: t(\"navigation.joinAsExpert\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                style: {\n                                                                    background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                                    animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"rrXTuHAe54Vm539jblkk+3aklEA=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme,\n        _themes__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Header.tsx\n"));

/***/ })

});